#!/usr/bin/env python3
"""
Script de teste para a funcionalidade de data de corte no filtro de Kalman
Demonstra como usar uma data específica para limitar os dados de treinamento
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import warnings
import os
from kalman import KalmanFilter
from datetime import datetime, timedelta
from analise_kalman_acoes_diversificadas import obter_dados_com_kalman_spread, criar_grafico_kalman

warnings.filterwarnings('ignore')

# Configurar matplotlib
import matplotlib
matplotlib.use('Agg')  # Backend para salvar sem display

def teste_data_corte_simples():
    """
    Teste simples com uma ação para demonstrar a funcionalidade de data de corte
    """
    print("🧪 TESTE DE DATA DE CORTE - FILTRO DE KALMAN")
    print("="*60)
    print("Este teste demonstra como usar uma data de corte para limitar")
    print("os dados de treinamento do filtro de Kalman.")
    print()
    
    # Ação de teste
    ticker = "PETR4.SA"
    nome = "Petrobras"
    
    print(f"📊 Testando com: {ticker} - {nome}")
    print()
    
    # Teste 1: Sem data de corte (dados completos)
    print("🔍 TESTE 1: Dados completos (sem corte)")
    print("-" * 40)
    
    resultado_completo = obter_dados_com_kalman_spread(ticker, nome, data_corte=None)
    
    if resultado_completo:
        dados_completo, previsoes_completo, kf_completo = resultado_completo
        print(f"✅ Dados obtidos: {len(dados_completo)} dias")
        print(f"   Período: {dados_completo.index[0].strftime('%Y-%m-%d')} a {dados_completo.index[-1].strftime('%Y-%m-%d')}")
        print(f"   Previsões: {len(previsoes_completo)} dias à frente")
        
        # Criar gráfico
        resultado_grafico = criar_grafico_kalman(ticker, nome + " (Completo)", dados_completo, previsoes_completo)
        if resultado_grafico:
            print(f"   📈 Gráfico salvo: {resultado_grafico.get('arquivo_grafico', 'N/A')}")
    else:
        print("❌ Falha ao obter dados completos")
        return False
    
    print()
    
    # Teste 2: Com data de corte (3 meses atrás)
    print("🔍 TESTE 2: Com data de corte (3 meses atrás)")
    print("-" * 40)
    
    # Data de corte: 3 meses atrás
    data_corte = datetime.now() - timedelta(days=90)
    data_corte_str = data_corte.strftime('%Y-%m-%d')
    
    print(f"📅 Data de corte: {data_corte_str}")
    
    resultado_cortado = obter_dados_com_kalman_spread(ticker, nome, data_corte=data_corte)
    
    if resultado_cortado:
        dados_cortado, previsoes_cortado, kf_cortado = resultado_cortado
        print(f"✅ Dados obtidos: {len(dados_cortado)} dias")
        print(f"   Período: {dados_cortado.index[0].strftime('%Y-%m-%d')} a {dados_cortado.index[-1].strftime('%Y-%m-%d')}")
        print(f"   Previsões: {len(previsoes_cortado)} dias à frente")
        
        # Criar gráfico
        resultado_grafico = criar_grafico_kalman(ticker, nome + " (Cortado)", dados_cortado, previsoes_cortado)
        if resultado_grafico:
            print(f"   📈 Gráfico salvo: {resultado_grafico.get('arquivo_grafico', 'N/A')}")
    else:
        print("❌ Falha ao obter dados com corte")
        return False
    
    print()
    
    # Comparação
    print("📊 COMPARAÇÃO DOS RESULTADOS")
    print("-" * 40)
    
    if resultado_completo and resultado_cortado:
        # Comparar últimos preços
        ultimo_preco_completo = dados_completo['Close'].iloc[-1]
        ultimo_preco_cortado = dados_cortado['Close'].iloc[-1]
        
        # Comparar previsões
        previsao_media_completo = np.mean(previsoes_completo) if previsoes_completo is not None else 0
        previsao_media_cortado = np.mean(previsoes_cortado) if previsoes_cortado is not None else 0
        
        print(f"Último preço (completo): R$ {ultimo_preco_completo:.2f}")
        print(f"Último preço (cortado):  R$ {ultimo_preco_cortado:.2f}")
        print(f"Diferença de preço:      R$ {abs(ultimo_preco_completo - ultimo_preco_cortado):.2f}")
        print()
        print(f"Previsão média (completo): R$ {previsao_media_completo:.2f}")
        print(f"Previsão média (cortado):  R$ {previsao_media_cortado:.2f}")
        print(f"Diferença de previsão:     R$ {abs(previsao_media_completo - previsao_media_cortado):.2f}")
        
        # Calcular variação percentual das previsões
        if ultimo_preco_completo > 0:
            var_completo = ((previsao_media_completo - ultimo_preco_completo) / ultimo_preco_completo) * 100
        else:
            var_completo = 0
            
        if ultimo_preco_cortado > 0:
            var_cortado = ((previsao_media_cortado - ultimo_preco_cortado) / ultimo_preco_cortado) * 100
        else:
            var_cortado = 0
        
        print()
        print(f"Variação prevista (completo): {var_completo:+.2f}%")
        print(f"Variação prevista (cortado):  {var_cortado:+.2f}%")
        
    print()
    print("✅ Teste concluído com sucesso!")
    print()
    print("💡 COMO USAR A FUNCIONALIDADE:")
    print("   1. Execute: python src/analise_kalman_acoes_diversificadas.py")
    print("   2. Escolha a opção '2' quando perguntado sobre data de corte")
    print("   3. Digite uma data no formato YYYY-MM-DD")
    print("   4. O filtro usará apenas dados até essa data para previsões")
    print()
    print("🎯 CASOS DE USO:")
    print("   • Testar precisão de previsões históricas")
    print("   • Simular análises em datas passadas")
    print("   • Validar performance do modelo")
    print("   • Backtesting de estratégias")
    
    return True

def main():
    """
    Função principal do teste
    """
    try:
        # Criar diretórios necessários
        os.makedirs('results/figures/kalman_analysis', exist_ok=True)
        
        # Executar teste
        sucesso = teste_data_corte_simples()
        
        if sucesso:
            print(f"\n🎉 Todos os testes passaram!")
            print(f"📁 Gráficos salvos em: results/figures/kalman_analysis/")
        else:
            print(f"\n❌ Alguns testes falharam")
            
    except Exception as e:
        print(f"\n❌ Erro durante o teste: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
